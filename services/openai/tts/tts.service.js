"use strict";

const fs = require("fs");
const {OpenAI} = require("openai");

const FileMixin = require("../../../mixins/file.mixin");
const configuration = {
  apiKey:
    process.env.OPENAI_API_KEY ||
    "********************************************************************************************************************************************************************",
};

const path = require("path");
const storageDir = path.join(__dirname, "./storage");

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "tts",
  mixins: [FileMixin],
  /**
   * Settings
   */
  settings: {},
  hooks: {
    before: {}
  },
  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    /**
     * Say a 'Hello' action.
     *
     * @returns
     */
    textToSpeech: {
      timeout: 6 * 60 * 1000,
      rest: {
        method: "GET",
        path: "/textToSpeech",
      },
      async handler(ctx) {
        const {text, voice = "alloy", model = "tts-1", speed = 1} = ctx.params;
        try {
          const openai = new OpenAI({apiKey: configuration.apiKey});

          const audio = await openai.audio.speech.create({
            model: model,
            voice: voice || "alloy",
            speed,
            input: text,
          });
          console.log("audio", audio);
          const dirPath = this.getDirPath('audio', storageDir);
          console.log("dirPath", dirPath);
          const filePath = this.getFilePath(`audio_${Date.now()}.mp3`, dirPath);
          console.log("filePath", filePath);
          const buffer = Buffer.from(await audio.arrayBuffer());
          await fs.promises.writeFile(filePath, buffer);
          const file = await ctx.call("files.createFromAudioBuffer", {buffer});
          return file;
        } catch (err) {
          return err;
        }
      },
    },
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {},

  /**
   * Service created lifecycle event handler
   */
  created() {
    if (!fs.existsSync(storageDir)) {
      fs.mkdirSync(storageDir, {recursive: true});
    }
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
