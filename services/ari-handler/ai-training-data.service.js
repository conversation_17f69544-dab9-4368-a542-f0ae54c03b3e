"use strict";

const DbMongoose = require("../../mixins/dbMongo.mixin");
const { MoleculerClientError } = require("moleculer").Errors;
const AiTrainingData = require("./ai-training-data.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const i18next = require("i18next");

module.exports = {
  name: "ai-training-data",
  mixins: [DbMongoose(AiTrainingData), BaseService, FunctionsCommon],

  /**
   * Default settings
   */
  settings: {
    /** REST Basepath */
    rest: "/ai-training-data",

    /** Public fields */
    fields: [
      "_id", "conversationId", "question", "answer", "category", "intent", 
      "confidence", "context", "source", "verified", "tags", "language",
      "priority", "usageCount", "lastUsed", "isActive", "createdAt", "updatedAt"
    ],

    /** Validator schema for entity */
    entityValidator: {
      conversationId: { type: "string", optional: true },
      question: { type: "string", min: 1 },
      answer: { type: "string", min: 1 },
      category: { 
        type: "enum", 
        values: ["support", "sales", "complaint", "billing", "technical", "general", "other"],
        optional: true 
      },
      intent: { 
        type: "enum", 
        values: [
          "product_inquiry", "billing", "technical_support", "complaint", 
          "order_status", "refund_request", "account_info", "service_info",
          "pricing", "features", "troubleshooting", "general_question", "other"
        ],
        optional: true 
      },
      confidence: { type: "number", min: 0, max: 1, optional: true },
      context: { type: "object", optional: true },
      source: { 
        type: "enum", 
        values: ["manual", "from_call", "imported", "generated"],
        optional: true 
      },
      verified: { type: "boolean", optional: true },
      tags: { type: "array", items: "string", optional: true },
      language: { type: "enum", values: ["vi", "en"], optional: true },
      priority: { type: "number", min: 1, max: 10, optional: true },
      isActive: { type: "boolean", optional: true }
    },
  },

  /**
   * Actions
   */
  actions: {
    /**
     * Thêm dữ liệu huấn luyện mới
     */
    addTrainingData: {
      rest: "POST /add",
      auth: "required",
      params: {
        question: "string",
        answer: "string",
        category: { 
          type: "enum", 
          values: ["support", "sales", "complaint", "billing", "technical", "general", "other"],
          optional: true 
        },
        intent: { 
          type: "enum", 
          values: [
            "product_inquiry", "billing", "technical_support", "complaint", 
            "order_status", "refund_request", "account_info", "service_info",
            "pricing", "features", "troubleshooting", "general_question", "other"
          ],
          optional: true 
        },
        tags: { type: "array", items: "string", optional: true },
        context: { type: "object", optional: true },
        priority: { type: "number", min: 1, max: 10, optional: true }
      },
      async handler(ctx) {
        try {
          const trainingData = {
            ...ctx.params,
            source: "manual",
            verified: false,
            isActive: true,
            createdAt: new Date()
          };

          const doc = await this.adapter.insert(trainingData);

          this.logger.info("Training data added", { id: doc._id, category: doc.category });

          // Emit event để trigger retraining
          this.broker.emit("ai.training.data.added", {
            id: doc._id,
            category: doc.category,
            intent: doc.intent
          });

          return {
            success: true,
            data: await this.transformDocuments(ctx, {}, doc)
          };
        } catch (error) {
          this.logger.error("Failed to add training data:", error);
          throw new MoleculerClientError(
            i18next.t("add_training_data_failed") || "Failed to add training data",
            500,
            "ADD_TRAINING_DATA_ERROR"
          );
        }
      }
    },

    /**
     * Thêm nhiều dữ liệu huấn luyện cùng lúc
     */
    addBulkTrainingData: {
      rest: "POST /bulk",
      auth: "required",
      params: {
        data: { 
          type: "array", 
          items: {
            type: "object",
            props: {
              question: "string",
              answer: "string",
              category: { type: "string", optional: true },
              intent: { type: "string", optional: true },
              tags: { type: "array", items: "string", optional: true },
              context: { type: "object", optional: true },
              priority: { type: "number", optional: true }
            }
          }
        }
      },
      async handler(ctx) {
        try {
          const { data } = ctx.params;

          // Validate và xử lý dữ liệu
          const processedData = data.map(item => ({
            ...item,
            source: "imported",
            verified: false,
            isActive: true,
            createdAt: new Date()
          }));

          // Insert bulk data
          const docs = await this.adapter.insertMany(processedData);

          this.logger.info(`Bulk training data added: ${docs.length} items`);

          // Emit event để trigger retraining
          this.broker.emit("ai.training.data.bulk.added", {
            count: docs.length,
            source: "imported"
          });

          return {
            success: true,
            count: docs.length,
            message: `Successfully added ${docs.length} training data items`
          };
        } catch (error) {
          this.logger.error("Failed to add bulk training data:", error);
          throw new MoleculerClientError(
            i18next.t("add_bulk_training_data_failed") || "Failed to add bulk training data",
            500,
            "ADD_BULK_TRAINING_DATA_ERROR"
          );
        }
      }
    },

    /**
     * Tìm kiếm dữ liệu huấn luyện
     */
    searchTrainingData: {
      rest: "GET /search",
      auth: "required",
      params: {
        query: { type: "string", optional: true },
        category: { type: "string", optional: true },
        intent: { type: "string", optional: true },
        verified: { type: "boolean", optional: true },
        isActive: { type: "boolean", optional: true },
        tags: { type: "array", items: "string", optional: true },
        page: { type: "number", default: 1, min: 1 },
        limit: { type: "number", default: 20, min: 1, max: 100 }
      },
      async handler(ctx) {
        try {
          const { 
            query, category, intent, verified, isActive, tags, page, limit 
          } = ctx.params;

          // Xây dựng query
          const searchQuery = { isDeleted: { $ne: true } };
          
          if (category) searchQuery.category = category;
          if (intent) searchQuery.intent = intent;
          if (verified !== undefined) searchQuery.verified = verified;
          if (isActive !== undefined) searchQuery.isActive = isActive;
          if (tags && tags.length > 0) searchQuery.tags = { $in: tags };

          // Full-text search nếu có query
          if (query) {
            searchQuery.$text = { $search: query };
          }

          // Thực hiện query với pagination
          const options = {
            page,
            limit,
            sort: query ? { score: { $meta: 'textScore' } } : { priority: -1, createdAt: -1 }
          };

          const results = await this.adapter.model.paginate(searchQuery, options);

          return {
            success: true,
            data: results.docs,
            pagination: {
              total: results.totalDocs,
              page: results.page,
              limit: results.limit,
              totalPages: results.totalPages,
              hasNextPage: results.hasNextPage,
              hasPrevPage: results.hasPrevPage
            }
          };
        } catch (error) {
          this.logger.error("Failed to search training data:", error);
          throw new MoleculerClientError(
            i18next.t("search_training_data_failed") || "Failed to search training data",
            500,
            "SEARCH_TRAINING_DATA_ERROR"
          );
        }
      }
    },

    /**
     * Xác minh dữ liệu huấn luyện
     */
    verifyTrainingData: {
      rest: "PUT /:id/verify",
      auth: "required",
      params: {
        id: "string",
        verified: "boolean"
      },
      async handler(ctx) {
        try {
          const { id, verified } = ctx.params;

          const updated = await this.adapter.updateById(id, {
            verified,
            updatedAt: new Date()
          });

          if (!updated) {
            throw new MoleculerClientError(
              i18next.t("training_data_not_found") || "Training data not found",
              404
            );
          }

          this.logger.info(`Training data verification updated: ${id}`, { verified });

          return {
            success: true,
            data: await this.transformDocuments(ctx, {}, updated)
          };
        } catch (error) {
          this.logger.error("Failed to verify training data:", error);
          throw new MoleculerClientError(
            error.message || "Failed to verify training data",
            error.code || 500,
            "VERIFY_TRAINING_DATA_ERROR"
          );
        }
      }
    },

    /**
     * Lấy thống kê dữ liệu huấn luyện
     */
    getTrainingStats: {
      rest: "GET /stats",
      auth: "required",
      async handler(ctx) {
        try {
          const stats = await this.adapter.model.aggregate([
            { $match: { isDeleted: { $ne: true } } },
            {
              $group: {
                _id: null,
                total: { $sum: 1 },
                verified: { $sum: { $cond: ["$verified", 1, 0] } },
                active: { $sum: { $cond: ["$isActive", 1, 0] } },
                byCategory: {
                  $push: {
                    category: "$category",
                    verified: "$verified",
                    active: "$isActive"
                  }
                },
                byIntent: {
                  $push: {
                    intent: "$intent",
                    verified: "$verified",
                    active: "$isActive"
                  }
                },
                bySource: {
                  $push: {
                    source: "$source",
                    verified: "$verified"
                  }
                }
              }
            }
          ]);

          // Xử lý thống kê chi tiết
          const result = stats[0] || { total: 0, verified: 0, active: 0 };
          
          // Thống kê theo category
          const categoryStats = {};
          if (result.byCategory) {
            result.byCategory.forEach(item => {
              if (!categoryStats[item.category]) {
                categoryStats[item.category] = { total: 0, verified: 0, active: 0 };
              }
              categoryStats[item.category].total++;
              if (item.verified) categoryStats[item.category].verified++;
              if (item.active) categoryStats[item.category].active++;
            });
          }

          // Thống kê theo intent
          const intentStats = {};
          if (result.byIntent) {
            result.byIntent.forEach(item => {
              if (!intentStats[item.intent]) {
                intentStats[item.intent] = { total: 0, verified: 0, active: 0 };
              }
              intentStats[item.intent].total++;
              if (item.verified) intentStats[item.intent].verified++;
              if (item.active) intentStats[item.intent].active++;
            });
          }

          // Thống kê theo source
          const sourceStats = {};
          if (result.bySource) {
            result.bySource.forEach(item => {
              if (!sourceStats[item.source]) {
                sourceStats[item.source] = { total: 0, verified: 0 };
              }
              sourceStats[item.source].total++;
              if (item.verified) sourceStats[item.source].verified++;
            });
          }

          return {
            success: true,
            stats: {
              total: result.total,
              verified: result.verified,
              active: result.active,
              unverified: result.total - result.verified,
              inactive: result.total - result.active,
              verificationRate: result.total > 0 ? (result.verified / result.total * 100).toFixed(2) : 0,
              activeRate: result.total > 0 ? (result.active / result.total * 100).toFixed(2) : 0,
              byCategory: categoryStats,
              byIntent: intentStats,
              bySource: sourceStats
            }
          };
        } catch (error) {
          this.logger.error("Failed to get training stats:", error);
          throw new MoleculerClientError(
            i18next.t("get_training_stats_failed") || "Failed to get training statistics",
            500,
            "GET_TRAINING_STATS_ERROR"
          );
        }
      }
    }
  },

  /**
   * Events
   */
  events: {
    "ai.training.data.used": {
      async handler(ctx) {
        const { id } = ctx.params;
        // Cập nhật usage count và last used
        await this.adapter.updateById(id, {
          $inc: { usageCount: 1 },
          lastUsed: new Date(),
          updatedAt: new Date()
        });
      }
    }
  },

  /**
   * Methods
   */
  methods: {
    
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
    this.logger.info("AI Training Data service created");
  },

  /**
   * Service started lifecycle event handler
   */
  started() {
    this.logger.info("AI Training Data service started");
  }
};
