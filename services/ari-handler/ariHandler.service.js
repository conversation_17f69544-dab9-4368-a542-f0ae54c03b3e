"use strict";

const AriClient = require("ari-client");

module.exports = {
  name: "arihandler",
  started: async function () {
    const client = await AriClient.connect(
      "http://localhost:8080",
      "cskh-1",
      "abc123@1"
    );

    this.logger.info("Connected to ARI");

    client.on("StasisStart", async (event, channel) => {
      const caller = channel.caller.number;
      this.logger.info(`📞 Cuộc gọi đến từ ${caller}`);

      await channel.answer();

      // Phát file âm thanh
      await channel.play({media: "sound:hello-world"});

      // <PERSON>hi âm (tu<PERSON> chọn)
      // await channel.record({ name: `record-${channel.id}`, format: "wav" });

      // Kết thúc sau 10s
      setTimeout(() => channel.hangup(), 10000);
    });

    // Bắt đầu app "myapp"
    client.start("myapp");
  },
};
