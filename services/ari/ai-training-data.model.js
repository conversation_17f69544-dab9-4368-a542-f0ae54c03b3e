const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const { AI_TRAINING_DATA } = require('../../constants/dbCollections');

const { Schema } = mongoose;

const aiTrainingDataSchema = new Schema({
  conversationId: { 
    type: String,
    index: true
  },
  question: { 
    type: String, 
    required: true,
    index: 'text' // Text index for search
  },
  answer: { 
    type: String, 
    required: true,
    index: 'text' // Text index for search
  },
  category: { 
    type: String, 
    required: true,
    enum: ['support', 'sales', 'complaint', 'billing', 'technical', 'general', 'other'],
    default: 'general',
    index: true
  },
  intent: { 
    type: String, 
    required: true,
    enum: [
      'product_inquiry', 'billing', 'technical_support', 'complaint', 
      'order_status', 'refund_request', 'account_info', 'service_info',
      'pricing', 'features', 'troubleshooting', 'general_question', 'other'
    ],
    default: 'general_question',
    index: true
  },
  confidence: { 
    type: Number, 
    min: 0,
    max: 1,
    default: 0.8
  },
  context: { 
    type: Schema.Types.Mixed, // Ngữ cảnh cuộc hội thoại
    default: {}
  },
  source: { 
    type: String, 
    required: true,
    enum: ['manual', 'from_call', 'imported', 'generated'],
    default: 'manual',
    index: true
  },
  verified: { 
    type: Boolean, 
    default: false,
    index: true
  },
  tags: [{ 
    type: String,
    index: true
  }],
  language: {
    type: String,
    default: 'vi',
    enum: ['vi', 'en'],
    index: true
  },
  priority: {
    type: Number,
    min: 1,
    max: 10,
    default: 5,
    index: true
  },
  usageCount: {
    type: Number,
    default: 0
  },
  lastUsed: {
    type: Date
  },
  isActive: { 
    type: Boolean, 
    default: true,
    index: true
  },
  isDeleted: { 
    type: Boolean, 
    default: false, 
    select: false 
  }
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

// Compound indexes for better query performance
aiTrainingDataSchema.index({ category: 1, intent: 1 });
aiTrainingDataSchema.index({ verified: 1, isActive: 1 });
aiTrainingDataSchema.index({ source: 1, createdAt: -1 });
aiTrainingDataSchema.index({ priority: -1, confidence: -1 });
aiTrainingDataSchema.index({ tags: 1, category: 1 });

// Text index for full-text search
aiTrainingDataSchema.index({ 
  question: 'text', 
  answer: 'text', 
  tags: 'text' 
}, {
  weights: {
    question: 10,
    answer: 5,
    tags: 1
  }
});

aiTrainingDataSchema.plugin(mongoosePaginate);

module.exports = mongoose.model(AI_TRAINING_DATA, aiTrainingDataSchema, AI_TRAINING_DATA);
