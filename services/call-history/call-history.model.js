const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const { CALL_HISTORY, USER, FILES } = require('../../constants/dbCollections');

const { Schema } = mongoose;

const callHistorySchema = new Schema({
  callId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  sipSessionId: {
    type: String,
    required: true,
    index: true
  },
  callerNumber: {
    type: String,
    required: true,
    index: true
  },
  receiverNumber: {
    type: String,
    required: true
  },
  callType: {
    type: String,
    required: true,
    enum: ['incoming', 'outgoing'],
    index: true
  },
  callStatus: {
    type: String,
    required: true,
    enum: ['connecting', 'completed', 'missed', 'rejected', 'ai_answered'],
    default: 'connecting',
    index: true
  },
  startTime: {
    type: Date,
    required: true,
    index: true
  },
  endTime: {
    type: Date
  },
  duration: {
    type: Number, // Thời lượng tính bằng giây
    default: 0
  },
  audioUrl: {
    type: String
  },
  audioFileId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: FILES
  },
  audioSize: {
    type: Number, // Kích thước file tính bằng bytes
    default: 0
  },
  aiHandled: {
    type: Boolean,
    default: false,
    index: true
  },
  aiTranscript: {
    type: String
  },
  aiResponse: {
    type: String
  },
  agentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: USER,
    index: true
  },
  customerInfo: {
    name: { type: String },
    phone: { type: String },
    customerId: { type: String }
  },
  metadata: {
    callQuality: {
      type: String,
      enum: ['good', 'average', 'poor']
    },
    recordingQuality: { type: String },
    deviceInfo: { type: Schema.Types.Mixed },
    networkInfo: { type: Schema.Types.Mixed }
  },
  isDeleted: {
    type: Boolean,
    default: false,
    select: false
  }
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

// Indexes for better query performance
callHistorySchema.index({ startTime: -1 });
callHistorySchema.index({ callerNumber: 1, startTime: -1 });
callHistorySchema.index({ agentId: 1, startTime: -1 });
callHistorySchema.index({ callType: 1, callStatus: 1 });
callHistorySchema.index({ aiHandled: 1, startTime: -1 });

callHistorySchema.plugin(mongoosePaginate);

module.exports = mongoose.model(CALL_HISTORY, callHistorySchema, CALL_HISTORY);
