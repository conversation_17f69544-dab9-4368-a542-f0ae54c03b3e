const {Server} = require("socket.io");

module.exports = {
  name: "socket",
  settings: {
    port: 3001,
  },
  dependencies: ["messages"],

  /**
   * Events
   */
  events: {
    
    "message.created": {
      async handler(ctx) {
        try {
          console.log("🔔 Socket service received message.created event:", ctx.params);

          const { messages, conversationId, senderId, senderRole } = ctx.params;
          // Get the full message details with populated data
          console.log("📨 Broadcasting message to conversation:", conversationId);

          // Emit to all clients in the conversation room
          if (this.io) {
            this.io.to(conversationId.toString()).emit("newMessage", messages);
            console.log("✅ Message broadcasted successfully");
          } else {
            console.log("❌ Socket.IO server not initialized");
          }
        } catch (error) {
          console.error("❌ Error handling message.created event:", error);
        }
      }
    },

    /**
     * Listen to message.read event from messages service
     */
    "message.read": {
      async handler(ctx) {
        try {
          console.log("🔔 Socket service received message.read event:", ctx.params);

          const { messageId, readBy, conversationId } = ctx.params;

          // Emit to all clients in the conversation room
          if (this.io) {
            this.io.to(conversationId.toString()).emit("messageRead", {
              messageId,
              readBy,
              conversationId
            });
            console.log("✅ Message read status broadcasted successfully");
          }
        } catch (error) {
          console.error("❌ Error handling message.read event:", error);
        }
      }
    }
  },

  async started() {
    console.log("🚀 Starting Socket service...");

    // Khởi tạo Socket.IO server
    this.io = new Server(this.settings.port, {
      transports: ["websocket"],
      path: "/socket",
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    console.log(`🔌 Socket.IO server initialized on port ${this.settings.port}`);

    // Phân biệt kết nối dựa trên namespace/path
    this.io.on("connection", (socket) => {
      console.log("🔌 New client connected", socket.id);

      socket.on("join", (conversationId) => {
        socket.join(conversationId);
        console.log(`👥 Client ${socket.id} joined conversation: ${conversationId}`);
      });

      socket.on("sendMessage", async (data) => {
        try {
          console.log("📤 Received sendMessage from client:", data);
          const msg = await this.broker.call("messages.create", data);
          this.io.to(data.conversationId.toString()).emit("newMessage", msg);
          console.log("✅ Message sent via socket successfully");
        } catch (error) {
          console.error("❌ Error sending message via socket:", error);
          socket.emit("error", { message: "Failed to send message" });
        }
      });

      socket.on("disconnect", () => {
        console.log("❌ Client disconnected", socket.id);
      });
    });

    this.logger.info(`✅ Socket.IO server is running on port ${this.settings.port}`);
    console.log("🎯 Socket service ready to receive events!");

  },

  /**
   * Actions
   */
  actions: {
    /**
     * Test action to verify socket service is working
     */
    test: {
      async handler(ctx) {
        return {
          status: "Socket service is running",
          port: this.settings.port,
          socketConnected: !!this.io,
          connectedClients: this.io ? this.io.engine.clientsCount : 0
        };
      }
    }
  },

  /**
   * Methods
   */
  methods: {
    /**
     * Broadcast message to specific conversation
     */
    broadcastToConversation(conversationId, event, data) {
      if (this.io) {
        this.io.to(conversationId.toString()).emit(event, data);
        console.log(`📡 Broadcasted ${event} to conversation ${conversationId}`);
      }
    }
  },

  stopped() {
    if (this.io) {
      console.log("🛑 Shutting down WebSocket server...");
      this.io.close();
    }
  },
};
